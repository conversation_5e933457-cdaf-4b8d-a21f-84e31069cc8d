[{"directory": "/home/<USER>/autovehicle/c_learn", "command": "g++ -std=c++17 -Wall -Wextra -g -I/home/<USER>/autovehicle/c_learn/include -DDEBUG -c /home/<USER>/autovehicle/c_learn/src/single.cpp", "file": "/home/<USER>/autovehicle/c_learn/src/single.cpp"}, {"directory": "/home/<USER>/autovehicle/c_learn", "command": "g++ -std=c++17 -Wall -Wextra -g -I/home/<USER>/autovehicle/c_learn/include -DDEBUG -c /home/<USER>/autovehicle/c_learn/src/math_utils.cpp", "file": "/home/<USER>/autovehicle/c_learn/src/math_utils.cpp"}, {"directory": "/home/<USER>/autovehicle/c_learn", "command": "g++ -std=c++17 -Wall -Wextra -g -I/home/<USER>/autovehicle/c_learn/include -DDEBUG -c /home/<USER>/autovehicle/c_learn/src/test_main.cpp", "file": "/home/<USER>/autovehicle/c_learn/src/test_main.cpp"}]