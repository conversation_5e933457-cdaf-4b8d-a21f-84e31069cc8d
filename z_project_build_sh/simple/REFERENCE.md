# 🚀 C++ 智能编译和项目配置脚本

一个集成了编译、调试、VSCode 配置和智能提示修复的一站式 C++ 开发工具。

## ✨ 主要特性

- 🔨 **智能编译**: 支持单文件和多文件编译模式
- 🚀 **一键运行**: 编译后可选择立即运行程序
- 🐛 **完整调试**: 自动生成 VSCode 调试配置
- 🔧 **智能提示**: 自动修复头文件识别和函数定义问题
- 📁 **项目初始化**: 一键创建标准 C++ 项目结构
- ⚡ **快速部署**: 适用于新项目或新环境的快速配置

## 🎯 快速开始

### 1. 初始化新项目
```bash
# 下载脚本到项目目录
chmod +x cpp_smart_build.sh

# 初始化项目结构和 VSCode 配置
./cpp_smart_build.sh --setup
```

这将创建以下目录结构：
```
your_project/
├── src/                    # 源文件目录
├── include/                # 头文件目录
├── build/bin/              # 可执行文件输出
├── .vscode/                # VSCode 配置
│   ├── tasks.json         # 构建任务
│   ├── launch.json        # 调试配置
│   └── settings.json      # 编辑器设置
├── .clangd                # clangd 配置
├── compile_commands.json  # 编译数据库
└── cpp_smart_build.sh     # 本脚本
```

### 2. 编译和运行

#### 单文件编译
```bash
# 编译并运行
./cpp_smart_build.sh src/main.cpp

# 仅编译
./cpp_smart_build.sh -c src/main.cpp

# 发布版本编译
./cpp_smart_build.sh -R src/main.cpp
```

#### 多文件编译
```bash
# 编译 src/ 目录下所有 .cpp 文件并运行
./cpp_smart_build.sh -m src/main.cpp

# 仅编译多文件项目
./cpp_smart_build.sh -m -c src/main.cpp
```

## 📖 命令行选项

### 编译选项
```bash
./cpp_smart_build.sh [选项] <cpp文件>

-s, --single      单文件编译（默认）
-m, --multi       多文件编译（编译src目录下所有cpp文件）
-r, --run         编译后立即运行（默认）
-c, --compile-only 仅编译，不运行
-d, --debug       调试模式编译（默认）
-R, --release     发布模式编译
```

### 项目管理选项
```bash
--setup           初始化项目（创建目录结构和VSCode配置）
--fix-intellisense 修复智能提示问题（重新生成编译数据库）
-h, --help        显示帮助信息
```

## 🎮 VSCode 集成使用

初始化项目后，在 VSCode 中可以使用以下功能：

### 构建任务（Ctrl+Shift+P → Tasks: Run Task）
- 🔨 **Smart: Single File Compile Only** - 单文件仅编译
- 🚀 **Smart: Single File Compile & Run** - 单文件编译并运行
- 📦 **Smart: Multi File Compile Only** - 多文件仅编译
- 🎯 **Smart: Multi File Compile & Run** - 多文件编译并运行
- ⚡ **Smart: Release Build** - 发布版本编译
- 🧹 **Smart: Clean Build** - 清理构建文件
- 🔧 **Smart: Generate Compile Commands** - 生成编译数据库

### 调试模式（F5 或 Debug 面板）
- 🐛 **Smart Debug: Current File** - 调试当前文件
- 🎯 **Smart Debug: Multi File Project** - 调试多文件项目
- 🔍 **Smart Debug: Step-by-Step** - 逐步调试（在入口处停止）

### 快捷键
- **F5**: 开始调试
- **Ctrl+Shift+B**: 选择构建任务
- **Ctrl+F5**: 运行不调试

## 🔧 解决智能提示问题

### 常见问题
- 头文件显示"找不到"的红色波浪线
- 自定义函数显示"未定义"的错误
- 智能提示不工作或不准确
- 跳转到定义功能失效

### 解决方案

#### 方法1：自动修复（推荐）
```bash
./cpp_smart_build.sh --fix-intellisense
```

#### 方法2：编译时自动更新
每次编译后会自动更新编译数据库：
```bash
./cpp_smart_build.sh -m src/main.cpp
```

#### 方法3：VSCode 任务
1. 按 `Ctrl+Shift+P`
2. 选择 `Tasks: Run Task`
3. 选择 `🔧 Smart: Generate Compile Commands`

#### 方法4：重启语言服务器
如果问题仍然存在：
1. 按 `Ctrl+Shift+P`
2. 输入 `clangd: Restart language server`
3. 或重新加载窗口：`Developer: Reload Window`

## 📚 使用示例

### 创建一个简单的多文件项目

1. **初始化项目**
```bash
./cpp_smart_build.sh --setup
```

2. **创建头文件** (include/calculator.h)
```cpp
#ifndef CALCULATOR_H
#define CALCULATOR_H

class Calculator {
public:
    int add(int a, int b);
    int multiply(int a, int b);
};

#endif // CALCULATOR_H
```

3. **创建实现文件** (src/calculator.cpp)
```cpp
#include "calculator.h"

int Calculator::add(int a, int b) {
    return a + b;
}

int Calculator::multiply(int a, int b) {
    return a * b;
}
```

4. **创建主文件** (src/main.cpp)
```cpp
#include <iostream>
#include "calculator.h"

int main() {
    Calculator calc;
    std::cout << "5 + 3 = " << calc.add(5, 3) << std::endl;
    std::cout << "5 * 3 = " << calc.multiply(5, 3) << std::endl;
    return 0;
}
```

5. **编译运行**
```bash
./cpp_smart_build.sh -m src/main.cpp
```

## 🛠️ 系统要求

- **编译器**: GCC 或 Clang（支持 C++17）
- **操作系统**: Linux, macOS, Windows (WSL)
- **VSCode**: 推荐安装 clangd 扩展
- **可选**: Python 3.x（用于高级功能）

## 🚀 部署到新环境

1. **复制脚本到新项目目录**
```bash
cp cpp_smart_build.sh /path/to/new/project/
cd /path/to/new/project/
chmod +x cpp_smart_build.sh
```

2. **初始化项目**
```bash
./cpp_smart_build.sh --setup
```

3. **开始开发**
- 将源文件放在 `src/` 目录
- 将头文件放在 `include/` 目录
- 在 VSCode 中打开项目文件夹

## 🎉 开始你的 C++ 开发之旅！

现在你拥有了一个完整的 C++ 开发环境：
- ✅ 智能编译和运行
- ✅ 完整的调试支持
- ✅ 准确的智能提示
- ✅ 标准的项目结构
- ✅ 一键部署到新环境

享受高效的 C++ 开发体验吧！ 🚀

---

## 💡 提示和技巧

- 每次添加新的 .cpp 或 .h 文件后，建议运行一次编译来更新编译数据库
- 如果使用外部库，可以修改脚本中的 `CXXFLAGS` 添加相应的包含路径
- 保持项目结构整洁，遵循 src/ 和 include/ 的分离原则
- 定期使用 `--fix-intellisense` 选项来确保智能提示正常工作
