# 📦 C++ 智能编译脚本 - 安装和使用指南

## 🎯 适用场景

这个脚本特别适合以下情况：
- 🆕 **新项目开发**：快速搭建 C++ 开发环境
- 🔄 **环境迁移**：在新机器上快速部署开发环境
- 🎓 **学习 C++**：专注于代码学习而不是环境配置
- 🐛 **调试问题**：解决 VSCode 智能提示和编译问题

## 📋 系统要求

### 必需组件
- **操作系统**: Linux, macOS, Windows (WSL)
- **编译器**: GCC 或 Clang（支持 C++17）
- **Shell**: Bash

### 推荐组件
- **VSCode**: 用于代码编辑和调试
- **clangd 扩展**: 提供智能提示（在 VSCode 扩展市场搜索 "clangd"）

### 检查系统环境
```bash
# 检查编译器
g++ --version
# 或
clang++ --version

# 检查 Bash
bash --version
```

## 🚀 安装步骤

### 方法1：直接下载使用
1. **下载脚本**
   ```bash
   # 将 cpp_smart_build.sh 复制到你的项目目录
   # 或者从现有项目复制
   cp /path/to/cpp_smart_build.sh ./
   ```

2. **添加执行权限**
   ```bash
   chmod +x cpp_smart_build.sh
   ```

3. **初始化项目**
   ```bash
   ./cpp_smart_build.sh --setup
   ```

### 方法2：全局安装（可选）
1. **复制到系统路径**
   ```bash
   sudo cp cpp_smart_build.sh /usr/local/bin/cpp-build
   sudo chmod +x /usr/local/bin/cpp-build
   ```

2. **在任何目录使用**
   ```bash
   cd /path/to/your/project
   cpp-build --setup
   ```

## 🎮 首次使用教程

### 步骤1：创建新项目
```bash
# 创建项目目录
mkdir my_cpp_project
cd my_cpp_project

# 复制脚本（如果还没有）
cp /path/to/cpp_smart_build.sh ./
chmod +x cpp_smart_build.sh

# 初始化项目
./cpp_smart_build.sh --setup
```

### 步骤2：创建第一个程序
```bash
# 创建头文件
cat > include/hello.h << 'EOF'
#ifndef HELLO_H
#define HELLO_H

void sayHello(const char* name);

#endif // HELLO_H
EOF

# 创建实现文件
cat > src/hello.cpp << 'EOF'
#include "hello.h"
#include <iostream>

void sayHello(const char* name) {
    std::cout << "Hello, " << name << "!" << std::endl;
}
EOF

# 创建主文件
cat > src/main.cpp << 'EOF'
#include "hello.h"

int main() {
    sayHello("C++ Developer");
    return 0;
}
EOF
```

### 步骤3：编译和运行
```bash
# 多文件编译并运行
./cpp_smart_build.sh -m src/main.cpp
```

### 步骤4：在 VSCode 中开发
```bash
# 打开 VSCode
code .
```

在 VSCode 中：
1. 打开任意 .cpp 文件
2. 按 **F5** 开始调试
3. 或按 **Ctrl+Shift+B** 选择构建任务

## 🔧 配置说明

### 自动生成的配置文件

#### `.vscode/tasks.json`
- 定义了 7 个构建任务
- 支持单文件和多文件编译
- 包含清理和智能提示修复任务

#### `.vscode/launch.json`
- 定义了 3 种调试模式
- 自动链接到构建任务
- 支持 GDB 调试器

#### `.vscode/settings.json`
- 配置 clangd 语言服务器
- 设置包含路径和编译标志
- 禁用默认的 C++ IntelliSense

#### `.clangd`
- clangd 语言服务器配置
- 启用代码检查和智能提示
- 配置编译标志

#### `compile_commands.json`
- 编译数据库
- 告诉 clangd 如何编译每个文件
- 自动生成和更新

## 🚨 故障排除

### 问题1：脚本无法执行
```bash
# 解决方案：检查权限
ls -la cpp_smart_build.sh
chmod +x cpp_smart_build.sh
```

### 问题2：编译器找不到
```bash
# 解决方案：安装编译器
# Ubuntu/Debian
sudo apt update
sudo apt install build-essential

# macOS
xcode-select --install

# 或安装 Homebrew 后
brew install gcc
```

### 问题3：VSCode 智能提示不工作
```bash
# 解决方案：修复智能提示
./cpp_smart_build.sh --fix-intellisense

# 然后在 VSCode 中
# Ctrl+Shift+P → "clangd: Restart language server"
```

### 问题4：找不到头文件
**检查项目结构：**
```bash
# 确保目录结构正确
tree .
# 应该看到：
# ├── src/
# ├── include/
# └── cpp_smart_build.sh
```

## 🔄 迁移现有项目

### 从其他构建系统迁移
1. **备份现有配置**
   ```bash
   mkdir backup
   cp -r .vscode backup/ 2>/dev/null || true
   ```

2. **重新组织文件结构**
   ```bash
   mkdir -p src include
   # 移动 .cpp 文件到 src/
   # 移动 .h 文件到 include/
   ```

3. **运行设置**
   ```bash
   ./cpp_smart_build.sh --setup
   ```

### 从 CMake 项目迁移
- 保留 CMakeLists.txt（如果需要）
- 使用智能编译脚本进行日常开发
- 大型项目发布时仍可使用 CMake

## 🎉 完成！

现在你已经拥有了一个完整的 C++ 开发环境：
- ✅ 智能编译系统
- ✅ VSCode 完整集成
- ✅ 调试支持
- ✅ 智能提示修复
- ✅ 标准项目结构

开始你的 C++ 开发之旅吧！ 🚀

---

## 📞 获取帮助

如果遇到问题：
1. 查看 `./cpp_smart_build.sh --help`
2. 检查 `README.md` 和 `QUICK_REFERENCE.md`
3. 确保系统满足要求
4. 尝试重新运行 `--setup` 或 `--fix-intellisense`
