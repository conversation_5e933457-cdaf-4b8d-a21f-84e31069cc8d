#!/bin/bash

# 🚀 C++ 智能编译和项目配置脚本
# 功能：编译、运行、生成VSCode配置、修复智能提示
# 作者: AI Assistant
# 版本: 2.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 编译器设置
CXX=${CXX:-g++}
CXXFLAGS="-std=c++17 -Wall -Wextra -g"
RELEASE_FLAGS="-O2 -DNDEBUG"
DEBUG_FLAGS="-O0 -DDEBUG"

# 目录结构
SRC_DIR="./src"
INCLUDE_DIR="./include"
OUTPUT_DIR="./build"
BIN_DIR="$OUTPUT_DIR/bin"
OBJ_DIR="$OUTPUT_DIR/obj"
VSCODE_DIR="./.vscode"

# 包含目录标志
INCLUDE_FLAGS="-I$INCLUDE_DIR"

# 创建项目目录结构
create_project_structure() {
    mkdir -p "$SRC_DIR" "$INCLUDE_DIR" "$BIN_DIR" "$OBJ_DIR" "$VSCODE_DIR"
    echo -e "${GREEN}✅ 目录结构创建完成${NC}"
}

# 生成编译数据库 (JSON格式)
generate_compile_commands_json() {
    
    if [[ ! -d "$SRC_DIR" ]]; then
        echo -e "${YELLOW}⚠️  src 目录不存在，跳过编译数据库生成${NC}"
        return
    fi
    
    # 查找所有 .cpp 文件
    local cpp_files=()
    while IFS= read -r -d '' file; do
        cpp_files+=("$file")
    done < <(find "$SRC_DIR" -maxdepth 1 -name "*.cpp" -print0)
    
    if [[ ${#cpp_files[@]} -eq 0 ]]; then
        echo -e "${YELLOW}⚠️  在 src 目录中没有找到 .cpp 文件${NC}"
        return
    fi
    
    # 生成 JSON 格式的编译数据库
    cat > compile_commands.json << 'EOF'
[
EOF
    
    local first_file=true
    for cpp_file in "${cpp_files[@]}"; do
        if [[ "$first_file" == false ]]; then
            echo "," >> compile_commands.json
        fi
        first_file=false
        
        local abs_file=$(realpath "$cpp_file")
        local abs_dir=$(realpath ".")
        local abs_include=$(realpath "$INCLUDE_DIR")
        
        cat >> compile_commands.json << EOF
  {
    "directory": "$abs_dir",
    "command": "$CXX $CXXFLAGS -I$abs_include -DDEBUG -c $abs_file",
    "file": "$abs_file"
  }
EOF
    done
    
    echo "" >> compile_commands.json
    echo "]" >> compile_commands.json
    
    echo -e "${GREEN}✅ 编译数据库-compile_commands.json 生成完成!${NC}"
}

# 生成 VSCode tasks.json
generate_vscode_tasks() {
    echo -e "${CYAN}⚙️  生成 VSCode tasks.json...${NC}"
    
    cat > "$VSCODE_DIR/tasks.json" << 'EOF'
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "🔨 Smart: Single File Compile Only",
            "detail": "智能编译：单文件仅编译（不运行）",
            "type": "shell",
            "command": "${workspaceFolder}/cpp_smart_build.sh",
            "args": [
                "-c",
                "${file}"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "🚀 Smart: Single File Compile & Run",
            "detail": "智能编译：单文件编译并运行",
            "type": "shell",
            "command": "${workspaceFolder}/cpp_smart_build.sh",
            "args": [
                "${file}"
            ],
            "group": {
                "kind": "test",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": true
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "📦 Smart: Multi File Compile Only",
            "detail": "智能编译：多文件仅编译（编译src目录下所有cpp文件）",
            "type": "shell",
            "command": "${workspaceFolder}/cpp_smart_build.sh",
            "args": [
                "-m",
                "-c",
                "${file}"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "🎯 Smart: Multi File Compile & Run",
            "detail": "智能编译：多文件编译并运行（编译src目录下所有cpp文件）",
            "type": "shell",
            "command": "${workspaceFolder}/cpp_smart_build.sh",
            "args": [
                "-m",
                "${file}"
            ],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": true
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "⚡ Smart: Release Build",
            "detail": "智能编译：发布版本编译（优化版本）",
            "type": "shell",
            "command": "${workspaceFolder}/cpp_smart_build.sh",
            "args": [
                "-R",
                "-c",
                "${file}"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "🚀 Smart: Release Build & Run",
            "detail": "智能编译：发布版本编译并运行（优化版本）",
            "type": "shell",
            "command": "${workspaceFolder}/cpp_smart_build.sh",
            "args": [
                "-R",
                "${file}"
            ],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": true
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "🧹 Smart: Clean Build",
            "detail": "清理构建文件",
            "type": "shell",
            "command": "rm",
            "args": [
                "-rf",
                "${workspaceFolder}/build"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            }
        },
        {
            "label": "🔧 Smart: Generate Compile Commands",
            "detail": "生成编译数据库以修复智能提示问题",
            "type": "shell",
            "command": "${workspaceFolder}/cpp_smart_build.sh",
            "args": [
                "--setup"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            }
        }
    ]
}
EOF
    
    echo -e "${GREEN}✅ tasks.json 生成完成${NC}"
}

# 生成 VSCode launch.json
generate_vscode_launch() {
    echo -e "${CYAN}🐛 生成 VSCode launch.json...${NC}"

    cat > "$VSCODE_DIR/launch.json" << 'EOF'
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "🐛 Smart Debug: Current File",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/build/bin/${fileBasenameNoExtension}",
      "args": [],
      "stopAtEntry": false,
      "cwd": "${workspaceFolder}",
      "environment": [],
      "externalConsole": false,
      "MIMode": "gdb",
      "miDebuggerPath": "/usr/bin/gdb",
      "setupCommands": [
        {
          "description": "Enable pretty-printing for gdb",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        },
        {
          "description": "Set Disassembly Flavor to Intel",
          "text": "-gdb-set disassembly-flavor intel",
          "ignoreFailures": true
        }
      ],
      "preLaunchTask": "🔨 Smart: Single File Compile Only",
      "internalConsoleOptions": "openOnSessionStart",
      "logging": {
        "moduleLoad": false,
        "trace": false,
        "engineLogging": false,
        "programOutput": true,
        "exceptions": false
      }
    },
    {
      "name": "🎯 Smart Debug: Multi File Project",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/build/bin/${fileBasenameNoExtension}",
      "args": [],
      "stopAtEntry": false,
      "cwd": "${workspaceFolder}",
      "environment": [],
      "externalConsole": false,
      "MIMode": "gdb",
      "miDebuggerPath": "/usr/bin/gdb",
      "setupCommands": [
        {
          "description": "Enable pretty-printing for gdb",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        },
        {
          "description": "Set Disassembly Flavor to Intel",
          "text": "-gdb-set disassembly-flavor intel",
          "ignoreFailures": true
        }
      ],
      "preLaunchTask": "📦 Smart: Multi File Compile Only",
      "internalConsoleOptions": "openOnSessionStart",
      "logging": {
        "moduleLoad": false,
        "trace": false,
        "engineLogging": false,
        "programOutput": true,
        "exceptions": false
      }
    },
    {
      "name": "🔍 Smart Debug: Step-by-Step (Stop at Entry)",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/build/bin/${fileBasenameNoExtension}",
      "args": [],
      "stopAtEntry": true,
      "cwd": "${workspaceFolder}",
      "environment": [],
      "externalConsole": false,
      "MIMode": "gdb",
      "miDebuggerPath": "/usr/bin/gdb",
      "setupCommands": [
        {
          "description": "Enable pretty-printing for gdb",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        },
        {
          "description": "Set Disassembly Flavor to Intel",
          "text": "-gdb-set disassembly-flavor intel",
          "ignoreFailures": true
        },
        {
          "description": "Enable detailed output",
          "text": "-gdb-set print elements 0",
          "ignoreFailures": true
        }
      ],
      "preLaunchTask": "🔨 Smart: Single File Compile Only",
      "internalConsoleOptions": "openOnSessionStart",
      "logging": {
        "moduleLoad": true,
        "trace": true,
        "engineLogging": false,
        "programOutput": true,
        "exceptions": true
      }
    }
  ]
}
EOF

    echo -e "${GREEN}✅ launch.json 生成完成${NC}"
}

# 生成 VSCode settings.json
generate_vscode_settings() {
    echo -e "${CYAN}⚙️  生成 VSCode settings.json...${NC}"

    cat > "$VSCODE_DIR/settings.json" << 'EOF'
{
  "cmake.configureOnOpen": false,
  "clangd.arguments": [
    "--header-insertion=iwyu",
    "--completion-style=detailed",
    "--function-arg-placeholders",
    "--fallback-style=file",
    "--enable-config",
    "--compile-commands-dir=${workspaceFolder}",
    "--background-index",
    "--clang-tidy"
  ],
  "clangd.fallbackFlags": [
    "-std=c++17",
    "-I${workspaceFolder}/include",
    "-DDEBUG"
  ],
  "C_Cpp.default.includePath": [
    "${workspaceFolder}/include",
    "${workspaceFolder}/src",
    "/usr/include/c++/**",
    "/usr/include"
  ],
  "C_Cpp.default.cppStandard": "c++17",
  "C_Cpp.default.compilerPath": "/usr/bin/g++",
  "C_Cpp.default.compileCommands": "${workspaceFolder}/compile_commands.json",
  "C_Cpp.intelliSenseEngine": "disabled",
  "code-runner.executorMap": {
    "cpp": "cd $workspaceRoot && ./cpp_smart_build.sh $fileName"
  },
  "code-runner.runInTerminal": true,
  "code-runner.saveFileBeforeRun": true,
  "files.associations": {
    "array": "cpp",
    "atomic": "cpp",
    "bit": "cpp",
    "*.tcc": "cpp",
    "cctype": "cpp",
    "clocale": "cpp",
    "cmath": "cpp",
    "cstdarg": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cwchar": "cpp",
    "cwctype": "cpp",
    "deque": "cpp",
    "map": "cpp",
    "set": "cpp",
    "unordered_map": "cpp",
    "vector": "cpp",
    "exception": "cpp",
    "algorithm": "cpp",
    "functional": "cpp",
    "iterator": "cpp",
    "memory": "cpp",
    "memory_resource": "cpp",
    "numeric": "cpp",
    "optional": "cpp",
    "random": "cpp",
    "string": "cpp",
    "string_view": "cpp",
    "system_error": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "utility": "cpp",
    "fstream": "cpp",
    "initializer_list": "cpp",
    "iosfwd": "cpp",
    "iostream": "cpp",
    "istream": "cpp",
    "limits": "cpp",
    "new": "cpp",
    "ostream": "cpp",
    "sstream": "cpp",
    "stdexcept": "cpp",
    "streambuf": "cpp",
    "typeinfo": "cpp",
    "cstring": "cpp"
  }
}
EOF

    echo -e "${GREEN}✅ settings.json 生成完成${NC}"
}

# 生成 .clangd 配置文件
generate_clangd_config() {
    echo -e "${CYAN}🔧 生成 .clangd 配置文件...${NC}"

    cat > ".clangd" << 'EOF'
CompileFlags:
  Add:
    - -std=c++17
    - -Wall
    - -Wextra
    - -Iinclude
    - -DDEBUG
  Remove:
    - -W*
    - -fcolor-diagnostics
    - -fdiagnostics-color=always
    - -fcoroutines-ts

Index:
  Background: Build
  StandardLibrary: Yes

Diagnostics:
  ClangTidy:
    Add:
      - readability-*
      - performance-*
      - modernize-*
      - bugprone-*
    Remove:
      - modernize-use-trailing-return-type
      - readability-magic-numbers
      - readability-uppercase-literal-suffix

InlayHints:
  Enabled: true
  ParameterNames: true
  DeducedTypes: true

Hover:
  ShowAKA: true

Completion:
  AllScopes: true
EOF

    echo -e "${GREEN}✅ .clangd 配置文件生成完成${NC}"
}

# 完整项目设置
setup_project() {
    echo -e "${BLUE}🚀 设置 C++ 智能编译项目...${NC}"
    echo ""

    create_project_structure
    generate_compile_commands_json
    generate_vscode_tasks
    generate_vscode_launch
    generate_vscode_settings
    generate_clangd_config

    echo ""
    echo -e "${GREEN}🎉 项目设置完成！${NC}"
    echo -e "${YELLOW}📁 项目结构：${NC}"
    echo -e "  ├── src/           # 源文件目录"
    echo -e "  ├── include/       # 头文件目录"
    echo -e "  ├── build/bin/     # 可执行文件输出"
    echo -e "  ├── .vscode/       # VSCode 配置"
    echo -e "  ├── .clangd        # clangd 配置"
    echo -e "  └── compile_commands.json  # 编译数据库"
    echo ""
    echo -e "${CYAN}💡 使用提示：${NC}"
    echo -e "  1. 将 .cpp 文件放在 src/ 目录"
    echo -e "  2. 将 .h 文件放在 include/ 目录"
    echo -e "  3. 在 VSCode 中按 F5 开始调试"
    echo -e "  4. 按 Ctrl+Shift+B 选择构建任务"
    echo ""
}

# 打印帮助信息
print_help() {
    echo -e "${CYAN}🚀 C++ 智能编译和项目配置脚本${NC}"
    echo -e "${YELLOW}用法:${NC}"
    echo -e "  $0 [选项] <cpp文件>          # 编译模式"
    echo -e "  $0 --setup                   # 项目设置模式"
    echo ""
    echo -e "${YELLOW}编译选项:${NC}"
    echo -e "  ${GREEN}-s, --single${NC}     单文件编译（默认）"
    echo -e "  ${GREEN}-m, --multi${NC}      多文件编译（编译src目录下所有cpp文件）"
    echo -e "  ${GREEN}-r, --run${NC}        编译后立即运行（默认）"
    echo -e "  ${GREEN}-c, --compile-only${NC} 仅编译，不运行"
    echo -e "  ${GREEN}-d, --debug${NC}      调试模式编译（默认）"
    echo -e "  ${GREEN}-R, --release${NC}    发布模式编译"
    echo ""
    echo -e "${YELLOW}项目设置选项:${NC}"
    echo -e "  ${GREEN}--setup${NC}          初始化项目（创建目录结构和VSCode配置）"
    echo -e "  ${GREEN}--fix-intellisense${NC} 修复智能提示问题（重新生成编译数据库）"
    echo ""
    echo -e "${YELLOW}其他选项:${NC}"
    echo -e "  ${GREEN}-h, --help${NC}       显示此帮助信息"
    echo ""
    echo -e "${YELLOW}编译示例:${NC}"
    echo -e "  $0 src/main.cpp              # 单文件编译并运行"
    echo -e "  $0 -c src/main.cpp           # 单文件仅编译"
    echo -e "  $0 -m src/main.cpp           # 多文件编译并运行"
    echo -e "  $0 -m -c src/main.cpp        # 多文件仅编译"
    echo -e "  $0 -R src/main.cpp           # 发布模式编译并运行"
    echo -e "  $0 -R -c src/main.cpp        # 发布模式仅编译"
    echo ""
    echo -e "${YELLOW}项目设置示例:${NC}"
    echo -e "  $0 --setup                   # 初始化新项目"
    echo -e "  $0 --fix-intellisense        # 修复智能提示"
}

# 检查文件是否存在
check_file_exists() {
    local file="$1"
    if [[ ! -f "$file" ]]; then
        echo -e "${RED}❌ 错误: 文件 '$file' 不存在${NC}"
        exit 1
    fi
}

# 获取文件名（不含扩展名）
get_basename() {
    local file="$1"
    basename "$file" .cpp
}

# 获取文件所在目录
get_dirname() {
    local file="$1"
    dirname "$file"
}

# 单文件编译
compile_single() {
    local source_file="$1"
    local run_after="$2"
    local build_mode="$3"

    echo -e "${YELLOW}源文件:${NC} $source_file"

    local basename=$(get_basename "$source_file")
    local output_file="$BIN_DIR/$basename"

    # 设置编译标志
    local compile_flags="$CXXFLAGS $INCLUDE_FLAGS"
    if [[ "$build_mode" == "release" ]]; then
        compile_flags="$compile_flags $RELEASE_FLAGS"
        echo -e "${YELLOW}构建模式:${NC} 发布版本"
    else
        compile_flags="$compile_flags $DEBUG_FLAGS"
        echo -e "${YELLOW}构建模式:${NC} 调试版本"
    fi

    echo -e "${YELLOW}编译命令:${NC} $CXX $compile_flags -o $output_file $source_file"

    # 编译
    if $CXX $compile_flags -o "$output_file" "$source_file"; then
        echo -e "${YELLOW}可执行文件:${NC} $output_file"

        # 生成编译数据库
        generate_compile_commands_json

        if [[ "$run_after" == "true" ]]; then
            echo -e "${CYAN}==================== 程序输出 ====================${NC}"
            "$output_file"
            local exit_code=$?
            echo -e "${YELLOW}程序退出码:${NC} $exit_code"
        fi
    else
        echo -e "${RED}❌ 编译失败!${NC}"
        exit 1
    fi
}

# 多文件编译
compile_multi() {
    local main_file="$1"
    local run_after="$2"
    local build_mode="$3"

    echo -e "${BLUE}🔨 多文件编译模式${NC}"

    local file_dir=$(get_dirname "$main_file")
    local basename=$(get_basename "$main_file")
    local output_file="$BIN_DIR/$basename"

    # 查找所有cpp文件（在src目录中）
    local cpp_files=()
    if [[ -d "$SRC_DIR" ]]; then
        while IFS= read -r -d '' file; do
            cpp_files+=("$file")
        done < <(find "$SRC_DIR" -maxdepth 1 -name "*.cpp" -print0)
    else
        # 如果没有src目录，则在当前文件所在目录查找
        while IFS= read -r -d '' file; do
            cpp_files+=("$file")
        done < <(find "$file_dir" -maxdepth 1 -name "*.cpp" -print0)
    fi

    if [[ ${#cpp_files[@]} -eq 0 ]]; then
        echo -e "${RED}❌ 错误: 没有找到 .cpp 文件${NC}"
        exit 1
    fi

    echo -e "${YELLOW}源文件目录:${NC} $SRC_DIR"
    echo -e "${YELLOW}包含目录:${NC} $INCLUDE_DIR"
    echo -e "${YELLOW}找到的 .cpp 文件:${NC}"
    for file in "${cpp_files[@]}"; do
        echo -e "  - $file"
    done

    # 设置编译标志
    local compile_flags="$CXXFLAGS $INCLUDE_FLAGS"
    if [[ "$build_mode" == "release" ]]; then
        compile_flags="$compile_flags $RELEASE_FLAGS"
        echo -e "${YELLOW}构建模式:${NC} 发布版本"
    else
        compile_flags="$compile_flags $DEBUG_FLAGS"
        echo -e "${YELLOW}构建模式:${NC} 调试版本"
    fi

    echo -e "${YELLOW}编译命令:${NC} $CXX $compile_flags -o $output_file ${cpp_files[*]}"

    # 编译
    if $CXX $compile_flags -o "$output_file" "${cpp_files[@]}"; then
        echo -e "${YELLOW}可执行文件:${NC} $output_file"

        # 生成编译数据库
        generate_compile_commands_json

        if [[ "$run_after" == "true" ]]; then
            echo -e "${CYAN}==================== 程序输出 ====================${NC}"
            "$output_file"
            local exit_code=$?
            echo -e "${YELLOW}程序退出码:${NC} $exit_code"
        fi
    else
        echo -e "${RED}❌ 编译失败!${NC}"
        exit 1
    fi
}

# 主函数
main() {
    local compile_mode="single"  # single 或 multi
    local run_after="true"       # true 或 false
    local build_mode="debug"     # debug 或 release
    local source_file=""
    local setup_mode=false
    local fix_intellisense=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--single)
                compile_mode="single"
                shift
                ;;
            -m|--multi)
                compile_mode="multi"
                shift
                ;;
            -r|--run)
                run_after="true"
                shift
                ;;
            -c|--compile-only)
                run_after="false"
                shift
                ;;
            -d|--debug)
                build_mode="debug"
                shift
                ;;
            -R|--release)
                build_mode="release"
                shift
                ;;
            --setup)
                setup_mode=true
                shift
                ;;
            --fix-intellisense)
                fix_intellisense=true
                shift
                ;;
            -h|--help)
                print_help
                exit 0
                ;;
            -*)
                echo -e "${RED}❌ 未知选项: $1${NC}"
                print_help
                exit 1
                ;;
            *)
                if [[ -z "$source_file" ]]; then
                    source_file="$1"
                else
                    echo -e "${RED}❌ 错误: 只能指定一个源文件${NC}"
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # 项目设置模式
    if [[ "$setup_mode" == true ]]; then
        setup_project
        exit 0
    fi

    # 修复智能提示模式
    if [[ "$fix_intellisense" == true ]]; then
        echo -e "${CYAN}🔧 修复智能提示问题...${NC}"
        create_project_structure
        generate_compile_commands_json
        generate_clangd_config
        echo -e "${GREEN}✅ 智能提示修复完成！请重启 VSCode 或重新加载窗口${NC}"
        exit 0
    fi

    # 编译模式需要源文件
    if [[ -z "$source_file" ]]; then
        echo -e "${RED}❌ 错误: 请指定一个 .cpp 源文件或使用 --setup 初始化项目${NC}"
        print_help
        exit 1
    fi

    # 检查文件是否存在
    check_file_exists "$source_file"

    # 创建输出目录
    create_project_structure

    # 根据模式编译
    if [[ "$compile_mode" == "single" ]]; then
        compile_single "$source_file" "$run_after" "$build_mode"
    else
        compile_multi "$source_file" "$run_after" "$build_mode"
    fi
}

# 运行主函数
main "$@"
