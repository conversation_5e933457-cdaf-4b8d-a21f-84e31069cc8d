# 🚀 C++ 项目快速创建器

一键创建现代 C++ 项目，包含完整的 VSCode 开发环境配置。

## 🎯 快速使用

```bash
# 创建项目
./quick_create_project.sh my_project

# 进入项目
cd my_project

# 在 VSCode 中打开
code .

# 按 F5 开始调试！
```

## ✨ 特性

- ✅ **一键创建** - 单个命令生成完整项目
- ✅ **现代 CMake** - 支持预设和增量编译
- ✅ **VSCode 集成** - 完整的调试和构建配置
- ✅ **智能提示** - clangd 语言服务器支持
- ✅ **中文注释** - 任务说明清晰易懂

## 📁 生成的项目结构

```
my_project/
├── src/main.cpp           # 程序入口
├── include/               # 头文件目录
├── scripts/build.sh       # 快速构建脚本
├── .vscode/               # VSCode 配置
├── CMakeLists.txt         # CMake 配置
└── CMakePresets.json      # CMake 预设
```

## 🎮 VSCode 任务

| 任务 | 说明 | 快捷键 |
|------|------|--------|
| 🔨 **CMake: Build Debug** | 构建调试版本 | Ctrl+Shift+B |
| ▶️ **CMake: Run Debug** | 运行调试版本 | - |
| ⚡ **CMake: Build Release** | 构建发布版本 | - |
| 🧹 **CMake: Clean** | 清理构建文件 | - |

## 📖 详细说明

查看 [使用说明.md](使用说明.md) 获取完整的使用指南。

---

**现在就开始你的 C++ 项目吧！** 🎉
