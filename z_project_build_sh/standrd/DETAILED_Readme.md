# 🚀 C++ 项目快速创建脚本使用说明

## 📋 脚本简介

`quick_create_project.sh` 是一个一键式 C++ 项目创建脚本，能够快速生成包含完整开发环境配置的现代 C++ 项目。

## 🎯 功能特性

- ✅ **一键创建** - 单个命令生成完整项目
- ✅ **现代 CMake** - 支持 CMake 3.16+ 和预设功能
- ✅ **VSCode 集成** - 完整的调试和构建配置
- ✅ **智能提示** - clangd 语言服务器支持
- ✅ **增量编译** - Ninja 快速构建
- ✅ **多种构建模式** - Debug/Release 预设
- ✅ **中文注释** - 任务说明清晰易懂

## 📖 使用方法

### 基本语法
```bash
./quick_create_project.sh <项目名称>
```

### 使用示例
```bash
# 创建一个名为 my_game 的项目
./quick_create_project.sh my_game

# 创建一个名为 calculator 的项目
./quick_create_project.sh calculator

# 创建一个名为 web_server 的项目
./quick_create_project.sh web_server
```

## 📁 生成的项目结构

```
项目名称/
├── 📄 配置文件
│   ├── CMakeLists.txt          # CMake 主配置文件
│   ├── CMakePresets.json       # CMake 预设配置
│   ├── .gitignore             # Git 忽略文件
│   └── .clang-format          # 代码格式化规则
├── 💻 源代码
│   ├── src/
│   │   └── main.cpp           # 程序入口点
│   └── include/               # 头文件目录
├── 🛠️ 构建脚本
│   └── scripts/
│       └── build.sh           # 快速构建脚本
├── ⚙️ VSCode 配置
│   └── .vscode/
│       ├── tasks.json         # 构建任务配置
│       ├── launch.json        # 调试配置
│       └── settings.json      # 编辑器设置
└── 🔧 构建输出
    └── build/                 # 构建目录（自动生成）
        ├── debug/bin/         # 调试版本可执行文件
        └── release/bin/       # 发布版本可执行文件
```

## 🎮 VSCode 任务说明

脚本生成的项目包含以下 VSCode 任务（按 `Ctrl+Shift+P` → `Tasks: Run Task` 选择）：

| 任务名称 | 图标 | 说明 | 使用场景 |
|---------|------|------|----------|
| **CMake: Configure Debug** | 🔧 | 配置调试环境 | 首次构建或修改CMakeLists.txt后 |
| **CMake: Configure Release** | 🚀 | 配置发布环境 | 准备发布版本时 |
| **CMake: Build Debug** | 🔨 | 构建调试版本 | 日常开发最常用 |
| **CMake: Build Release** | ⚡ | 构建发布版本 | 构建最终发布版本 |
| **CMake: Run Debug** | ▶️ | 运行调试版本 | 快速测试代码 |
| **CMake: Run Release** | 🏃 | 运行发布版本 | 测试发布版本性能 |
| **CMake: Clean** | 🧹 | 清理构建文件 | 构建出错时重新开始 |
| **CMake: Clean All** | 💥 | 删除整个build目录 | 彻底清理项目 |

## 🚀 快速开始

### 1. 创建项目
```bash
./quick_create_project.sh my_awesome_project
```

### 2. 进入项目目录
```bash
cd my_awesome_project
```

### 3. 在 VSCode 中打开
```bash
code .
```

### 4. 开始开发
- **按 F5** - 调试运行
- **按 Ctrl+F5** - 直接运行
- **按 Ctrl+Shift+B** - 构建项目

## ⌨️ 常用快捷键

| 快捷键 | 功能 |
|--------|------|
| **F5** | 开始调试 |
| **Ctrl+F5** | 运行不调试 |
| **Ctrl+Shift+B** | 构建项目 |
| **Ctrl+Shift+P** | 打开命令面板 |

## 🔧 命令行构建

如果不使用 VSCode，也可以使用命令行：

```bash
# 方法1: 使用快速脚本
./scripts/build.sh debug    # 构建调试版本
./scripts/build.sh release  # 构建发布版本

# 方法2: 使用 CMake 命令
cmake --preset=debug && cmake --build --preset=debug
cmake --preset=release && cmake --build --preset=release

# 运行程序
./build/debug/bin/项目名称     # 运行调试版本
./build/release/bin/项目名称   # 运行发布版本
```

## 🛠️ 系统要求

- **CMake** 3.16 或更高版本
- **Ninja** 构建工具
- **GCC** 或 **Clang** 编译器
- **clangd** 语言服务器（可选，用于智能提示）
- **VSCode**（推荐，需要安装 CMake Tools 扩展）

## 📝 注意事项

1. **项目名称要求**：
   - 只能包含字母、数字、下划线和连字符
   - 不能以数字开头
   - 建议使用小写字母和下划线

2. **首次使用**：
   - 确保脚本有执行权限：`chmod +x quick_create_project.sh`
   - 确保系统已安装必要的工具

3. **VSCode 扩展**：
   - 推荐安装 `CMake Tools` 扩展
   - 推荐安装 `clangd` 扩展（而不是 C/C++ 扩展）

## 🔍 故障排除

### 问题1：脚本无法执行
```bash
# 解决方案：添加执行权限
chmod +x quick_create_project.sh
```

### 问题2：CMake 版本过低
```bash
# 检查版本
cmake --version

# 如果版本低于 3.16，需要升级 CMake
```

### 问题3：找不到 Ninja
```bash
# 安装 Ninja
sudo apt install ninja-build  # Ubuntu/Debian
brew install ninja            # macOS
```

### 问题4：VSCode 中无法调试
1. 确保项目已构建：按 `Ctrl+Shift+B`
2. 检查可执行文件是否存在：`ls build/debug/bin/`
3. 重新加载 VSCode 窗口：`Ctrl+Shift+P` → `Developer: Reload Window`

## 🎉 开始你的 C++ 项目之旅！

现在你已经掌握了使用方法，快去创建你的第一个项目吧：

```bash
./quick_create_project.sh hello_world
cd hello_world
code .
# 按 F5 开始调试！
```
