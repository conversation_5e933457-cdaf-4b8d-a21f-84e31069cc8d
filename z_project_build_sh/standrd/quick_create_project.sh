#!/bin/bash

# 超简单的 C++ 项目创建脚本
# 使用方法: ./quick_create_project.sh project_name

if [ $# -eq 0 ]; then
    echo "用法: $0 <项目名称>"
    echo "示例: $0 my_project"
    exit 1
fi

PROJECT_NAME="$1"

echo "🚀 创建项目: $PROJECT_NAME"

# 创建目录结构
mkdir -p "$PROJECT_NAME"/{src,include,scripts,.vscode}

# 创建 main.cpp
cat > "$PROJECT_NAME/src/main.cpp" << 'EOF'
#include <iostream>

int main() {
    std::cout << "Hello, World!" << std::endl;
    return 0;
}
EOF

# 创建 CMakeLists.txt
cat > "$PROJECT_NAME/CMakeLists.txt" << EOF
cmake_minimum_required(VERSION 3.16)

project($PROJECT_NAME
    VERSION 1.0.0
    DESCRIPTION "Modern C++ Project"
    LANGUAGES CXX
)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug CACHE STRING "Build type" FORCE)
endif()

# 输出目录设置 - 这很重要！
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY \${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY \${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY \${CMAKE_BINARY_DIR}/lib)

# 编译选项
if(MSVC)
    add_compile_options(/W4)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(/Od /Zi)
        add_compile_definitions(DEBUG)
    else()
        add_compile_options(/O2)
        add_compile_definitions(NDEBUG)
    endif()
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(-g -O0)
        add_compile_definitions(DEBUG)
    else()
        add_compile_options(-O2)
        add_compile_definitions(NDEBUG)
    endif()
endif()

include_directories(\${CMAKE_SOURCE_DIR}/include)
file(GLOB_RECURSE SOURCES "src/*.cpp")
file(GLOB_RECURSE HEADERS "include/*.h")
add_executable(\${PROJECT_NAME} \${SOURCES} \${HEADERS})
EOF

# 创建 CMakePresets.json
cat > "$PROJECT_NAME/CMakePresets.json" << 'EOF'
{
  "version": 3,
  "configurePresets": [
    {
      "name": "debug",
      "displayName": "Debug Configuration",
      "description": "Debug build configuration",
      "generator": "Ninja",
      "binaryDir": "${sourceDir}/build/debug",
      "cacheVariables": {
        "CMAKE_BUILD_TYPE": "Debug",
        "CMAKE_EXPORT_COMPILE_COMMANDS": "ON"
      }
    },
    {
      "name": "release",
      "displayName": "Release Configuration",
      "description": "Release build configuration",
      "generator": "Ninja",
      "binaryDir": "${sourceDir}/build/release",
      "cacheVariables": {
        "CMAKE_BUILD_TYPE": "Release",
        "CMAKE_EXPORT_COMPILE_COMMANDS": "ON"
      }
    }
  ],
  "buildPresets": [
    {
      "name": "debug",
      "configurePreset": "debug",
      "displayName": "Debug Build"
    },
    {
      "name": "release",
      "configurePreset": "release",
      "displayName": "Release Build"
    }
  ]
}
EOF

# 创建构建脚本
cat > "$PROJECT_NAME/scripts/build.sh" << 'EOF'
#!/bin/bash
BUILD_TYPE=${1:-debug}
echo "🔨 构建 $BUILD_TYPE 版本..."
cmake --preset=$BUILD_TYPE
cmake --build --preset=$BUILD_TYPE
echo "✅ 构建完成！"
EOF

chmod +x "$PROJECT_NAME/scripts/build.sh"

# 创建 VSCode 配置文件
# tasks.json
cat > "$PROJECT_NAME/.vscode/tasks.json" << 'EOF'
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "CMake: Configure Debug",
            "detail": "🔧 配置调试环境 - 首次构建或修改CMakeLists.txt后使用",
            "type": "shell",
            "command": "/usr/local/bin/cmake",
            "args": [
                "--preset=debug"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "CMake: Configure Release",
            "detail": "🚀 配置发布环境 - 准备发布版本时使用",
            "type": "shell",
            "command": "/usr/local/bin/cmake",
            "args": [
                "--preset=release"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "CMake: Build Debug",
            "detail": "🔨 构建调试版本 - 日常开发最常用",
            "type": "shell",
            "command": "/usr/local/bin/cmake",
            "args": [
                "--build",
                "--preset=debug"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            },
            "dependsOn": "CMake: Configure Debug"
        },
        {
            "label": "CMake: Build Release",
            "detail": "⚡ 构建发布版本 - 构建最终发布版本",
            "type": "shell",
            "command": "/usr/local/bin/cmake",
            "args": [
                "--build",
                "--preset=release"
            ],
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            },
            "dependsOn": "CMake: Configure Release"
        },
        {
            "label": "CMake: Run Debug",
            "detail": "▶️ 运行调试版本 - 快速测试代码",
            "type": "shell",
            "command": "${workspaceFolder}/build/debug/bin/${workspaceFolderBasename}",
            "group": {
                "kind": "test",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": true
            },
            "dependsOn": "CMake: Build Debug"
        },
        {
            "label": "CMake: Run Release",
            "detail": "🏃 运行发布版本 - 测试发布版本性能",
            "type": "shell",
            "command": "${workspaceFolder}/build/release/bin/${workspaceFolderBasename}",
            "group": {
                "kind": "test",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": true
            },
            "dependsOn": "CMake: Build Release"
        },
        {
            "label": "CMake: Clean",
            "detail": "🧹 清理构建文件 - 构建出错时重新开始",
            "type": "shell",
            "command": "/usr/local/bin/cmake",
            "args": [
                "--build",
                "${workspaceFolder}/build/debug",
                "--target",
                "clean"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            }
        },
        {
            "label": "CMake: Clean All",
            "detail": "💥 删除整个build目录 - 彻底清理项目",
            "type": "shell",
            "command": "rm",
            "args": [
                "-rf",
                "${workspaceFolder}/build"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            }
        }
    ]
}
EOF

# launch.json
cat > "$PROJECT_NAME/.vscode/launch.json" << 'EOF'
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "CMake Debug",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/build/debug/bin/${workspaceFolderBasename}",
      "args": [],
      "stopAtEntry": false,
      "cwd": "${workspaceFolder}",
      "environment": [],
      "externalConsole": false,
      "MIMode": "gdb",
      "miDebuggerPath": "/usr/bin/gdb",
      "setupCommands": [
        {
          "description": "Enable pretty-printing for gdb",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        },
        {
          "description": "Set Disassembly Flavor to Intel",
          "text": "-gdb-set disassembly-flavor intel",
          "ignoreFailures": true
        }
      ],
      "preLaunchTask": "CMake: Build Debug",
      "internalConsoleOptions": "openOnSessionStart",
      "logging": {
        "moduleLoad": false,
        "trace": false,
        "engineLogging": false,
        "programOutput": true,
        "exceptions": false
      }
    },
    {
      "name": "CMake Release",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/build/release/bin/${workspaceFolderBasename}",
      "args": [],
      "stopAtEntry": false,
      "cwd": "${workspaceFolder}",
      "environment": [],
      "externalConsole": false,
      "MIMode": "gdb",
      "miDebuggerPath": "/usr/bin/gdb",
      "setupCommands": [
        {
          "description": "Enable pretty-printing for gdb",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        },
        {
          "description": "Set Disassembly Flavor to Intel",
          "text": "-gdb-set disassembly-flavor intel",
          "ignoreFailures": true
        }
      ],
      "preLaunchTask": "CMake: Build Release",
      "internalConsoleOptions": "openOnSessionStart",
      "logging": {
        "moduleLoad": false,
        "trace": false,
        "engineLogging": false,
        "programOutput": true,
        "exceptions": false
      }
    }
  ]
}
EOF

# settings.json
cat > "$PROJECT_NAME/.vscode/settings.json" << 'EOF'
{
  "cmake.configureOnOpen": true,
  "cmake.buildDirectory": "${workspaceFolder}/build/${buildKit}-${buildType}",
  "cmake.generator": "Ninja",
  "cmake.preferredGenerators": ["Ninja", "Unix Makefiles"],
  "cmake.configureArgs": [
    "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
  ],
  "clangd.arguments": [
    "--header-insertion=iwyu",
    "--completion-style=detailed",
    "--function-arg-placeholders",
    "--fallback-style=file",
    "--enable-config"
  ],
  "clangd.fallbackFlags": [
    "-std=c++17"
  ],
  "files.associations": {
    "array": "cpp",
    "atomic": "cpp",
    "bit": "cpp",
    "*.tcc": "cpp",
    "cctype": "cpp",
    "clocale": "cpp",
    "cmath": "cpp",
    "cstdarg": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cwchar": "cpp",
    "cwctype": "cpp",
    "deque": "cpp",
    "map": "cpp",
    "set": "cpp",
    "unordered_map": "cpp",
    "vector": "cpp",
    "exception": "cpp",
    "algorithm": "cpp",
    "functional": "cpp",
    "iterator": "cpp",
    "memory": "cpp",
    "memory_resource": "cpp",
    "numeric": "cpp",
    "optional": "cpp",
    "random": "cpp",
    "string": "cpp",
    "string_view": "cpp",
    "system_error": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "utility": "cpp",
    "fstream": "cpp",
    "initializer_list": "cpp",
    "iosfwd": "cpp",
    "iostream": "cpp",
    "istream": "cpp",
    "limits": "cpp",
    "new": "cpp",
    "ostream": "cpp",
    "sstream": "cpp",
    "stdexcept": "cpp",
    "streambuf": "cpp",
    "typeinfo": "cpp",
    "cstring": "cpp"
  }
}
EOF

# 创建其他配置文件
# .gitignore
cat > "$PROJECT_NAME/.gitignore" << 'EOF'
# Build directories
build/
cmake-build-*/

# IDE files
.vscode/settings.json
.idea/
*.swp
*.swo

# Compiled Object files
*.o
*.obj

# Executables
*.exe
*.out
*.app

# Libraries
*.lib
*.a
*.la
*.lo
*.dll
*.so
*.dylib

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake

# Other
.DS_Store
Thumbs.db
EOF

# .clang-format
cat > "$PROJECT_NAME/.clang-format" << 'EOF'
BasedOnStyle: Google
IndentWidth: 2
ColumnLimit: 100
EOF

echo "✅ 项目创建完成！"
echo "📂 项目位置: $PROJECT_NAME"
echo ""
echo "✨ 包含的配置："
echo "- ✅ VSCode 调试配置 (.vscode/)"
echo "- ✅ CMake 配置"
echo "- ✅ clangd 智能提示"
echo "- ✅ 代码格式化"
echo ""
echo "下一步："
echo "1. cd $PROJECT_NAME"
echo "2. code ."
echo "3. 按 F5 开始调试"
