{
    "folders": [
        {
            "path": "."
        }
    ],
    "settings": {
        // 完全禁用 C/C++ 扩展的自动任务生成
        "C_Cpp.intelliSenseEngine": "Disabled",
        "C_Cpp.autocomplete": "Disabled",
        "C_Cpp.errorSquiggles": "Disabled",
        "C_Cpp.configurationWarnings": "Disabled",
        "C_Cpp.autoAddFileAssociations": false,
        "C_Cpp.suggestSnippets": false,
        "C_Cpp.debugShortcut": false,
        "C_Cpp.vcpkg.enabled": false,
        
        // 禁用 C/C++ Runner 扩展
        "C_Cpp_Runner.enableWarnings": false,
        "C_Cpp_Runner.showCompilationTime": false,
        
        // 禁用任务自动发现
        "task.autoDetect": "off",
        
        // 使用 clangd
        "clangd.arguments": [
            "--header-insertion=iwyu",
            "--completion-style=detailed",
            "--function-arg-placeholders",
            "--fallback-style=file",
            "--enable-config",
            "--compile-commands-dir=${workspaceFolder}",
            "--background-index",
            "--clang-tidy"
        ],
        "clangd.fallbackFlags": [
            "-std=c++17",
            "-I${workspaceFolder}/include",
            "-DDEBUG"
        ]
    },
    "extensions": {
        "recommendations": [
            "llvm-vs-code-extensions.vscode-clangd",
            "ms-vscode.cmake-tools",
            "formulahendry.code-runner"
        ],
        "unwantedRecommendations": [
            "ms-vscode.cpptools",
            "ms-vscode.cpptools-extension-pack",
            "franneck94.c-cpp-runner"
        ]
    }
}
