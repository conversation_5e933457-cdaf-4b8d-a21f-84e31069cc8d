CompileFlags:
  Add:
    - -std=c++17
    - -Wall
    - -Wextra
    - -Iinclude
    - -DDEBUG
  Remove:
    - -W*
    - -fcolor-diagnostics
    - -fdiagnostics-color=always
    - -fcoroutines-ts

Index:
  Background: Build
  StandardLibrary: Yes

Diagnostics:
  ClangTidy:
    Add:
      - readability-*
      - performance-*
      - modernize-*
      - bugprone-*
    Remove:
      - modernize-use-trailing-return-type
      - readability-magic-numbers
      - readability-uppercase-literal-suffix

InlayHints:
  Enabled: true
  ParameterNames: true
  DeducedTypes: true

Hover:
  ShowAKA: true

Completion:
  AllScopes: true
