{
  "cmake.configureOnOpen": false,
  "cmake.buildDirectory": "${workspaceFolder}/build/${buildKit}-${buildType}",
  "cmake.generator": "Ninja",
  "cmake.preferredGenerators": ["Ninja", "Unix Makefiles"],
  "cmake.configureArgs": [
    "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
  ],
  "clangd.arguments": [
    "--header-insertion=iwyu",
    "--completion-style=detailed",
    "--function-arg-placeholders",
    "--fallback-style=file",
    "--enable-config",
    "--compile-commands-dir=${workspaceFolder}",
    "--background-index",
    "--clang-tidy"
  ],
  "clangd.fallbackFlags": [
    "-std=c++17",
    "-I${workspaceFolder}/include",
    "-DDEBUG"
  ],
  "C_Cpp.default.includePath": [
    "${workspaceFolder}/include",
    "${workspaceFolder}/src",
    "/usr/include/c++/**",
    "/usr/include"
  ],
  "C_Cpp.default.cppStandard": "c++17",
  "C_Cpp.default.compilerPath": "/usr/bin/g++",
  "C_Cpp.default.compileCommands": "${workspaceFolder}/compile_commands.json",
  "C_Cpp.intelliSenseEngine": "disabled",
  "C_Cpp.autocomplete": "Disabled",
  "C_Cpp.errorSquiggles": "Disabled",
  "C_Cpp.configurationWarnings": "Disabled",
  "C_Cpp.autoAddFileAssociations": false,
  "C_Cpp.suggestSnippets": false,
  "C_Cpp.debugShortcut": false,
  "C_Cpp.vcpkg.enabled": false,

  // 禁用 C/C++ Runner 扩展功能
  "C_Cpp_Runner.enableWarnings": false,
  "C_Cpp_Runner.showCompilationTime": false,
  "C_Cpp_Runner.useMsvc": false,
  "C_Cpp_Runner.useAddressSanitizer": false,
  "C_Cpp_Runner.useUndefinedSanitizer": false,
  "C_Cpp_Runner.useLeakSanitizer": false,

  // 禁用任务自动发现
  "task.autoDetect": "off",
  "typescript.tsc.autoDetect": "off",
  "grunt.autoDetect": "off",
  "gulp.autoDetect": "off",
  "jake.autoDetect": "off",
  "npm.autoDetect": "off",
  "code-runner.executorMap": {
    "cpp": "cd $workspaceRoot && ./cpp_smart_build.sh $fileName"
  },
  "code-runner.runInTerminal": true,
  "code-runner.saveFileBeforeRun": true,
  "files.associations": {
    "array": "cpp",
    "atomic": "cpp",
    "bit": "cpp",
    "*.tcc": "cpp",
    "cctype": "cpp",
    "clocale": "cpp",
    "cmath": "cpp",
    "cstdarg": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cwchar": "cpp",
    "cwctype": "cpp",
    "deque": "cpp",
    "map": "cpp",
    "set": "cpp",
    "unordered_map": "cpp",
    "vector": "cpp",
    "exception": "cpp",
    "algorithm": "cpp",
    "functional": "cpp",
    "iterator": "cpp",
    "memory": "cpp",
    "memory_resource": "cpp",
    "numeric": "cpp",
    "optional": "cpp",
    "random": "cpp",
    "string": "cpp",
    "string_view": "cpp",
    "system_error": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "utility": "cpp",
    "fstream": "cpp",
    "initializer_list": "cpp",
    "iosfwd": "cpp",
    "iostream": "cpp",
    "istream": "cpp",
    "limits": "cpp",
    "new": "cpp",
    "ostream": "cpp",
    "sstream": "cpp",
    "stdexcept": "cpp",
    "streambuf": "cpp",
    "typeinfo": "cpp",
    "cstring": "cpp"
  }
}