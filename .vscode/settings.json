{
  "cmake.configureOnOpen": false,
  "cmake.buildDirectory": "${workspaceFolder}/build/${buildKit}-${buildType}",
  "cmake.generator": "Ninja",
  "cmake.preferredGenerators": ["Ninja", "Unix Makefiles"],
  "cmake.configureArgs": [
    "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
  ],
  // 完全禁用 C/C++ 扩展功能
  "C_Cpp.intelliSenseEngine": "Disabled",
  "C_Cpp.autocomplete": "Disabled",
  "C_Cpp.errorSquiggles": "Disabled",
  "C_Cpp.configurationWarnings": "Disabled",
  "C_Cpp.autoAddFileAssociations": false,
  "C_Cpp.suggestSnippets": false,
  "C_Cpp.default.browse.limitSymbolsToIncludedHeaders": false,
  "C_Cpp.workspaceParsingPriority": "low",

  // 优化 clangd 配置
  "clangd.arguments": [
    "--header-insertion=iwyu",
    "--completion-style=detailed",
    "--function-arg-placeholders",
    "--fallback-style=file",
    "--enable-config",
    "--compile-commands-dir=${workspaceFolder}",
    "--background-index",
    "--clang-tidy",
    "--log=verbose"
  ],
  "clangd.fallbackFlags": [
    "-std=c++17",
    "-I${workspaceFolder}/include",
    "-DDEBUG"
  ],
  "clangd.onConfigChanged": "restart",
  "clangd.restartAfterCrash": true,
  "code-runner.executorMap": {
    "cpp": "cd $workspaceRoot && ./cpp_smart_build.sh $fileName"
  },
  "code-runner.runInTerminal": true,
  "code-runner.saveFileBeforeRun": true,
  "files.associations": {
    "array": "cpp",
    "atomic": "cpp",
    "bit": "cpp",
    "*.tcc": "cpp",
    "cctype": "cpp",
    "clocale": "cpp",
    "cmath": "cpp",
    "cstdarg": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cwchar": "cpp",
    "cwctype": "cpp",
    "deque": "cpp",
    "map": "cpp",
    "set": "cpp",
    "unordered_map": "cpp",
    "vector": "cpp",
    "exception": "cpp",
    "algorithm": "cpp",
    "functional": "cpp",
    "iterator": "cpp",
    "memory": "cpp",
    "memory_resource": "cpp",
    "numeric": "cpp",
    "optional": "cpp",
    "random": "cpp",
    "string": "cpp",
    "string_view": "cpp",
    "system_error": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "utility": "cpp",
    "fstream": "cpp",
    "initializer_list": "cpp",
    "iosfwd": "cpp",
    "iostream": "cpp",
    "istream": "cpp",
    "limits": "cpp",
    "new": "cpp",
    "ostream": "cpp",
    "sstream": "cpp",
    "stdexcept": "cpp",
    "streambuf": "cpp",
    "typeinfo": "cpp",
    "cstring": "cpp"
  }
}