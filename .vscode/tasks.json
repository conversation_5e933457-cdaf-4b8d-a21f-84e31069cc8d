{"version": "2.0.0", "tasks": [{"label": "🔨 Smart: Single File Compile Only", "detail": "智能编译：单文件仅编译（不运行）", "type": "shell", "command": "${workspaceFolder}/cpp_smart_build.sh", "args": ["-c", "${file}"], "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "🚀 Smart: Single File Compile & Run", "detail": "智能编译：单文件编译并运行", "type": "shell", "command": "${workspaceFolder}/cpp_smart_build.sh", "args": ["${file}"], "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "📦 Smart: Multi File Compile Only", "detail": "智能编译：多文件仅编译（编译src目录下所有cpp文件）", "type": "shell", "command": "${workspaceFolder}/cpp_smart_build.sh", "args": ["-m", "-c", "${file}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "🎯 Smart: Multi File Compile & Run", "detail": "智能编译：多文件编译并运行（编译src目录下所有cpp文件）", "type": "shell", "command": "${workspaceFolder}/cpp_smart_build.sh", "args": ["-m", "${file}"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "⚡ Smart: Release Build", "detail": "智能编译：发布版本编译（优化版本）", "type": "shell", "command": "${workspaceFolder}/cpp_smart_build.sh", "args": ["-R", "-c", "${file}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "🚀 Smart: Release Build & Run", "detail": "智能编译：发布版本编译并运行（优化版本）", "type": "shell", "command": "${workspaceFolder}/cpp_smart_build.sh", "args": ["-R", "${file}"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "🧹 Smart: Clean Build", "detail": "清理构建文件", "type": "shell", "command": "rm", "args": ["-rf", "${workspaceFolder}/build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}]}