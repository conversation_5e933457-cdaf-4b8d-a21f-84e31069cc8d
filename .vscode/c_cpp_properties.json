{"version": 4, "configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/include", "${workspaceFolder}/src", "/usr/include/c++/**", "/usr/include/**"], "defines": ["DEBUG"], "compilerPath": "/usr/bin/g++", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "linux-gcc-x64", "compileCommands": "${workspaceFolder}/compile_commands.json", "configurationProvider": "ms-vscode.cmake-tools"}]}