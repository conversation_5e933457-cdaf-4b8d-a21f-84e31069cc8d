{"version": "0.2.0", "configurations": [{"name": "🐛 Smart Debug: Current File", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/${fileBasenameNoExtension}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "preLaunchTask": "🔨 Smart: Single File Compile Only"}, {"name": "🎯 Smart Debug: Multi File Project", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/${fileBasenameNoExtension}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "preLaunchTask": "📦 Smart: Multi File Compile Only"}, {"name": "⚡ Smart Debug: Release Version", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/${fileBasenameNoExtension}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "preLaunchTask": "⚡ Smart: Release Build"}]}