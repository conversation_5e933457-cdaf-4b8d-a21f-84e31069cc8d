{"version": "0.2.0", "configurations": [{"name": "🐛 Smart Debug: Current File", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/${fileBasenameNoExtension}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "🔨 Smart: Single File Compile Only", "internalConsoleOptions": "openOnSessionStart", "logging": {"moduleLoad": false, "trace": false, "engineLogging": false, "programOutput": true, "exceptions": false}}, {"name": "🎯 Smart Debug: Multi File Project", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/${fileBasenameNoExtension}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "📦 Smart: Multi File Compile Only", "internalConsoleOptions": "openOnSessionStart", "logging": {"moduleLoad": false, "trace": false, "engineLogging": false, "programOutput": true, "exceptions": false}}, {"name": "🔍 Smart Debug: Step-by-Step (Stop at Entry)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/${fileBasenameNoExtension}", "args": [], "stopAtEntry": true, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}, {"description": "Enable detailed output", "text": "-gdb-set print elements 0", "ignoreFailures": true}], "preLaunchTask": "🔨 Smart: Single File Compile Only", "internalConsoleOptions": "openOnSessionStart", "logging": {"moduleLoad": true, "trace": true, "engineLogging": false, "programOutput": true, "exceptions": true}}, {"name": "⚡ Smart Debug: Release Version", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/${fileBasenameNoExtension}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "⚡ Smart: Release Build", "internalConsoleOptions": "openOnSessionStart", "logging": {"moduleLoad": false, "trace": false, "engineLogging": false, "programOutput": true, "exceptions": false}}]}