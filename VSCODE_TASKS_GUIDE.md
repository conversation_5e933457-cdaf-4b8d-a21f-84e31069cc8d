# 🎮 VSCode 任务使用指南

## 📋 任务分组说明

VSCode 中的任务被分为两个主要组：

### 🔨 Build 组（构建任务）

用于编译代码，不运行程序：

- **🔨 Smart: Single File Compile Only** - 单文件仅编译（默认构建任务）
- **📦 Smart: Multi File Compile Only** - 多文件仅编译
- **⚡ Smart: Release Build** - 发布版本编译
- **🧹 Smart: Clean Build** - 清理构建文件
- **🔧 Smart: Generate Compile Commands** - 生成编译数据库

### 🚀 Test 组（测试任务）

用于编译并运行程序：

- **🚀 Smart: Single File Compile & Run** - 单文件编译并运行（默认测试任务）
- **🎯 Smart: Multi File Compile & Run** - 多文件编译并运行

## 🎯 如何运行任务

### 方法 1：使用快捷键（推荐）

#### 运行构建任务

```
Ctrl+Shift+B
```

- 直接运行默认构建任务（🔨 Smart: Single File Compile Only）
- 或显示构建任务列表供选择

#### 运行测试任务

```
Ctrl+Shift+P → Tasks: Run Test Task
```

- 直接运行默认测试任务（🚀 Smart: Single File Compile & Run）
- 或显示测试任务列表供选择

### 方法 2：使用命令面板

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入以下命令之一：

#### 运行任务

```
Tasks: Run Task
```

显示所有可用任务的列表

#### 运行构建任务

```
Tasks: Run Build Task
```

显示构建任务列表或直接运行默认构建任务

#### 运行测试任务

```
Tasks: Run Test Task
```

显示测试任务列表或直接运行默认测试任务

### 方法 3：使用菜单

- **Terminal** → **Run Task...** → 选择任务
- **Terminal** → **Run Build Task...** → 选择构建任务

## 🔄 任务执行流程

### 编译流程

1. **单文件编译**：

   - 选择 🔨 或 🚀 任务
   - 编译当前打开的 .cpp 文件
   - 使用 include/ 目录中的头文件

2. **多文件编译**：
   - 选择 📦 或 🎯 任务
   - 编译 src/ 目录下所有 .cpp 文件
   - 自动链接所有源文件

### 运行流程

- **仅编译任务**：编译完成后停止
- **编译并运行任务**：编译成功后自动运行程序

## 🎨 任务状态指示

### 终端面板显示

- **🔨** = 编译中
- **✅** = 编译成功
- **❌** = 编译失败
- **🚀** = 程序运行中

### 问题面板

- 编译错误和警告会显示在 **Problems** 面板中
- 点击错误可直接跳转到对应代码行

## 🛠️ 自定义任务

### 修改默认任务

在 `.vscode/tasks.json` 中：

- 将 `"isDefault": true` 设置给你想要的默认任务
- 其他任务设置为 `"isDefault": false`

### 添加新任务

可以在 `tasks.json` 中添加自定义任务，例如：

```json
{
  "label": "🧪 My Custom Task",
  "type": "shell",
  "command": "your_command_here",
  "group": "build"
}
```

## 🚨 常见问题

### Q: 为什么按 Ctrl+Shift+B 没有反应？

**A:** 检查是否有默认构建任务：

1. 打开 `.vscode/tasks.json`
2. 确保有一个任务设置了 `"isDefault": true`

### Q: 如何快速运行"编译并运行"任务？

**A:** 使用以下方法：

1. `Alt+B` → `Tasks: Run Test Task`
2. 或者设置自定义快捷键

### Q: 任务运行失败怎么办？

**A:** 检查以下几点：

1. 确保脚本有执行权限：`chmod +x cpp_smart_build.sh`
2. 检查文件路径是否正确
3. 查看终端输出的错误信息

### Q: 如何同时看到编译输出和程序输出？

**A:**

- 使用 🚀 或 🎯 任务（编译并运行）
- 终端会显示完整的编译和运行过程

## 💡 使用技巧

### 1. 快速切换任务

- 最近使用的任务会出现在任务列表顶部
- 使用 `Ctrl+Shift+P` → `Tasks: Rerun Last Task` 重复上次任务

### 2. 并行运行任务

- 可以同时运行多个任务（如果它们不冲突）
- 每个任务在独立的终端中运行

### 3. 任务依赖

- 某些任务可能依赖其他任务
- VSCode 会自动按顺序执行依赖任务

### 4. 自动保存

- 任务运行前会自动保存当前文件
- 确保编译的是最新代码

## 🎯 推荐工作流程

### 日常开发

1. **编写代码**
2. **按 `Ctrl+Shift+B`** 快速编译检查语法
3. **按 `Ctrl+Shift+P` → `Tasks: Run Test Task`** 运行程序测试

### 调试开发

1. **使用构建任务编译**
2. **按 `F5` 启动调试器**
3. **设置断点进行调试**

### 发布准备

1. **使用 ⚡ Smart: Release Build** 编译优化版本
2. **测试发布版本功能**
3. **清理构建文件准备发布**

---

💡 **记住**：合理使用任务分组可以大大提高开发效率！构建任务用于检查代码，测试任务用于运行验证。
