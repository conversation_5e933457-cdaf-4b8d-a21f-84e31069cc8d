#include "math_utils.h"
#include <iostream>
#include <string>

int main() {
  std::cout << "🚀 C++ 智能编译系统测试程序" << std::endl;
  std::cout << "================================" << std::endl;

  // 测试数学工具函数
  int a = 10, b = 5;
  std::cout << "数学运算测试:" << std::endl;
  std::cout << a << " + " << b << " = " << add(a, b) << std::endl;
  std::cout << a << " * " << b << " = " << multiply(a, b) << std::endl;

  // 测试阶乘
  int n = 5;
  std::cout << n << "! = " << factorial(n) << std::endl;

  // 测试质数判断
  std::cout << "\n质数测试:" << std::endl;
  for (int i = 2; i <= 20; i++) {
    if (isPrime(i)) {
      std::cout << i << " 是质数" << std::endl;
    }
  }

  // 用户交互
  std::cout << "\n请输入您的姓名: ";
  std::string name;
  std::getline(std::cin, name);

  std::cout << "你好, " << name << "! 欢迎使用智能编译系统!" << std::endl;

  return 0;
}
