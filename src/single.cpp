#include <iostream>

class Base {
public:
  Base() { std::cout << "base constructor\n" << std::endl; }
  virtual ~Base() { std::cout << "base destructor\n" << std::endl; }
};

class deliver : public Base {
public:
  deliver() { std::cout << "deliver constructor\n" << std::endl; }
  ~deliver() { std::cout << "deliver destructor\n" << std::endl; }
};

int main() {
  Base *base = new Base();
  delete base;
  deliver *deli = new deliver();
  delete deli;
  std::cout << "--------" << std::endl;
  Base *poly = new deliver();
  delete poly;
  std::cin.get();
}