{"version": "2.0.0", "tasks": [{"label": "CMake: Configure Debug", "type": "shell", "command": "/usr/local/bin/cmake", "args": ["--preset=debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "CMake: Configure Release", "type": "shell", "command": "/usr/local/bin/cmake", "args": ["--preset=release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "CMake: Build Debug", "type": "shell", "command": "/usr/local/bin/cmake", "args": ["--build", "--preset=debug"], "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}, "dependsOn": "CMake: Configure Debug"}, {"label": "CMake: Build Release", "type": "shell", "command": "/usr/local/bin/cmake", "args": ["--build", "--preset=release"], "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}, "dependsOn": "CMake: Configure Release"}, {"label": "CMake: Run Debug", "type": "shell", "command": "${workspaceFolder}/build/debug/bin/${workspaceFolderBasename}", "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": true}, "dependsOn": "CMake: Build Debug"}, {"label": "CMake: Run Release", "type": "shell", "command": "${workspaceFolder}/build/release/bin/${workspaceFolderBasename}", "group": {"kind": "test", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": true}, "dependsOn": "CMake: Build Release"}, {"label": "CMake: Clean", "type": "shell", "command": "/usr/local/bin/cmake", "args": ["--build", "${workspaceFolder}/build/debug", "--target", "clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "CMake: Clean All", "type": "shell", "command": "rm", "args": ["-rf", "${workspaceFolder}/build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}]}