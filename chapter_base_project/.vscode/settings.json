{"cmake.configureOnOpen": true, "cmake.buildDirectory": "${workspaceFolder}/build/${buildKit}-${buildType}", "cmake.generator": "Ninja", "cmake.preferredGenerators": ["Ninja", "Unix Makefiles"], "cmake.configureArgs": ["-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"], "clangd.arguments": ["--header-insertion=iwyu", "--completion-style=detailed", "--function-arg-placeholders", "--fallback-style=file", "--enable-config"], "clangd.fallbackFlags": ["-std=c++17"], "code-runner.executorMap": {"cpp": "cd $workspaceRoot && cmake --build --preset=debug && ./build/debug/bin/$workspaceRootBasename"}, "code-runner.runInTerminal": true, "code-runner.saveFileBeforeRun": true, "code-runner.saveAllFilesBeforeRun": true, "code-runner.ignoreSelection": true, "code-runner.fileDirectoryAsCwd": false, "files.associations": {"array": "cpp", "atomic": "cpp", "bit": "cpp", "*.tcc": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "map": "cpp", "set": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "ostream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "typeinfo": "cpp", "cstring": "cpp"}}