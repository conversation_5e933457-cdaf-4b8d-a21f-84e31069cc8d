{"version": "0.2.0", "configurations": [{"name": "CMake Debug", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/debug/bin/${workspaceFolderBasename}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "CMake: Build Debug", "internalConsoleOptions": "openOnSessionStart", "logging": {"moduleLoad": false, "trace": false, "engineLogging": false, "programOutput": true, "exceptions": false}}, {"name": "CMake Release", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/release/bin/${workspaceFolderBasename}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "CMake: Build Release", "internalConsoleOptions": "openOnSessionStart", "logging": {"moduleLoad": false, "trace": false, "engineLogging": false, "programOutput": true, "exceptions": false}}]}