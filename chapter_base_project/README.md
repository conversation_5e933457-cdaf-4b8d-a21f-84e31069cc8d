# C++ CMake Project

## 项目结构
```
├── src/              # 源代码文件
├── include/          # 头文件
├── build/            # CMake 构建输出
│   ├── debug/        # Debug 构建
│   └── release/      # Release 构建
├── docs/             # 文档
├── tests/            # 测试文件
├── scripts/          # 构建脚本
├── CMakeLists.txt    # CMake 配置
└── CMakePresets.json # CMake 预设
```

## 构建说明

### 使用 CMake 命令行
```bash
# 配置 Debug
cmake --preset=debug

# 构建 Debug
cmake --build --preset=debug

# 配置 Release
cmake --preset=release

# 构建 Release
cmake --build --preset=release
```

### 使用 VS Code
- 安装 CMake Tools 扩展
- 按 F5 进行调试
- 使用 Ctrl+Shift+P -> Tasks: Run Task 选择 CMake 任务

### 支持的 IDE
- VS Code (推荐安装 CMake Tools 扩展)
- CLion (原生支持)
- Qt Creator (原生支持)
- Visual Studio (原生支持)
