#ifndef MATH_UTILS_H
#define MATH_UTILS_H

/**
 * @brief 数学工具函数库
 * 
 * 提供常用的数学计算功能
 */

/**
 * @brief 计算两个整数的和
 * @param a 第一个整数
 * @param b 第二个整数
 * @return 两个整数的和
 */
int add(int a, int b);

/**
 * @brief 计算两个整数的乘积
 * @param a 第一个整数
 * @param b 第二个整数
 * @return 两个整数的乘积
 */
int multiply(int a, int b);

/**
 * @brief 计算阶乘
 * @param n 要计算阶乘的数
 * @return n的阶乘
 */
long long factorial(int n);

/**
 * @brief 判断是否为质数
 * @param n 要判断的数
 * @return 如果是质数返回true，否则返回false
 */
bool isPrime(int n);

#endif // MATH_UTILS_H
